---
description: Documentation for text encoding models in Ultralytics YOLOE, supporting both OpenAI CLIP and Apple MobileCLIP implementations for vision-language tasks.
keywords: YOLOE, text encoding, CLIP, MobileCLIP, TextModel, vision-language models, embeddings, Ultralytics, deep learning
---

# Reference for `ultralytics/nn/text_model.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/text_model.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/text_model.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/nn/text_model.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.nn.text_model.TextModel

<br><br><hr><br>

## ::: ultralytics.nn.text_model.CLIP

<br><br><hr><br>

## ::: ultralytics.nn.text_model.MobileCLIP

<br><br><hr><br>

## ::: ultralytics.nn.text_model.MobileCLIPTS

<br><br><hr><br>

## ::: ultralytics.nn.text_model.build_text_model

<br><br>
