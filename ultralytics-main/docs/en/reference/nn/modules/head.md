---
description: Explore docs covering Ultralytics YOLO detection, pose & RTDETRDecoder. Comprehensive guides to help you understand Ultralytics nn modules.
keywords: Ultralytics, YOLO, Detection, Pose, RTDETRDecoder, nn modules, guides
---

# Reference for `ultralytics/nn/modules/head.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/modules/head.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/modules/head.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/nn/modules/head.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.nn.modules.head.Detect

<br><br><hr><br>

## ::: ultralytics.nn.modules.head.Segment

<br><br><hr><br>

## ::: ultralytics.nn.modules.head.OBB

<br><br><hr><br>

## ::: ultralytics.nn.modules.head.Pose

<br><br><hr><br>

## ::: ultralytics.nn.modules.head.Classify

<br><br><hr><br>

## ::: ultralytics.nn.modules.head.WorldDetect

<br><br><hr><br>

## ::: ultralytics.nn.modules.head.LRPCHead

<br><br><hr><br>

## ::: ultralytics.nn.modules.head.YOLOEDetect

<br><br><hr><br>

## ::: ultralytics.nn.modules.head.YOLOESegment

<br><br><hr><br>

## ::: ultralytics.nn.modules.head.RTDETRDecoder

<br><br><hr><br>

## ::: ultralytics.nn.modules.head.v10Detect

<br><br>
