---
description: Learn how to set up and customize MLflow logging for Ultralytics YOLO. Log metrics, parameters, and model artifacts easily.
keywords: MLflow, Ultralytics YOLO, logging, metrics, parameters, model artifacts, setup, tracking, customization
---

# Reference for `ultralytics/utils/callbacks/mlflow.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/mlflow.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/mlflow.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/mlflow.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.callbacks.mlflow.sanitize_dict

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.mlflow.on_pretrain_routine_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.mlflow.on_train_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.mlflow.on_fit_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.mlflow.on_train_end

<br><br>
