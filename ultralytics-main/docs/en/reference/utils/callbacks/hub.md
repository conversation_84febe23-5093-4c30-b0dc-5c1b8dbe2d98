---
description: Explore detailed guides on Ultralytics callbacks, including pretrain, model save, train start/end, and more. Enhance your ML training workflows with ease.
keywords: Ultralytics, callbacks, pretrain, model save, train start, train end, validation, predict, export, training, machine learning
---

# Reference for `ultralytics/utils/callbacks/hub.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/hub.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/hub.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/hub.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.callbacks.hub.on_pretrain_routine_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.hub.on_pretrain_routine_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.hub.on_fit_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.hub.on_model_save

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.hub.on_train_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.hub.on_train_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.hub.on_val_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.hub.on_predict_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.hub.on_export_start

<br><br>
