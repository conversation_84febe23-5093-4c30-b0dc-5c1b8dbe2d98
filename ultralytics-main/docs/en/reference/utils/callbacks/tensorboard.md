---
description: Learn how to integrate and use TensorBoard with Ultralytics for effective model training visualization.
keywords: Ultralytics, TensorBoard, callbacks, machine learning, training visualization, logging
---

# Reference for `ultralytics/utils/callbacks/tensorboard.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/tensorboard.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/tensorboard.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/tensorboard.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.callbacks.tensorboard._log_scalars

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.tensorboard._log_tensorboard_graph

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.tensorboard.on_pretrain_routine_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.tensorboard.on_train_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.tensorboard.on_train_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.tensorboard.on_fit_epoch_end

<br><br>
