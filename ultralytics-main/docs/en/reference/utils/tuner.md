---
description: Explore how to use ultralytics.utils.tuner.py for efficient hyperparameter tuning with <PERSON>. Learn implementation details and example usage.
keywords: Ultralytics, tuner, hyperparameter tuning, <PERSON>ne, YOLO, machine learning, AI, optimization
---

# Reference for `ultralytics/utils/tuner.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/tuner.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/tuner.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/tuner.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.tuner.run_ray_tune

<br><br>
