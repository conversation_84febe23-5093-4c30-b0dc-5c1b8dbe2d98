---
description: Explore the utility functions and context managers in Ultralytics like WorkingDirectory, increment_path, file_size, and more. Enhance your file handling in Python.
keywords: Ultralytics, file utilities, Python, WorkingDirectory, increment_path, file_size, file_age, contexts, file handling, file management
---

# Reference for `ultralytics/utils/files.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/files.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/files.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/files.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.files.WorkingDirectory

<br><br><hr><br>

## ::: ultralytics.utils.files.spaces_in_path

<br><br><hr><br>

## ::: ultralytics.utils.files.increment_path

<br><br><hr><br>

## ::: ultralytics.utils.files.file_age

<br><br><hr><br>

## ::: ultralytics.utils.files.file_date

<br><br><hr><br>

## ::: ultralytics.utils.files.file_size

<br><br><hr><br>

## ::: ultralytics.utils.files.get_latest_run

<br><br><hr><br>

## ::: ultralytics.utils.files.update_models

<br><br>
