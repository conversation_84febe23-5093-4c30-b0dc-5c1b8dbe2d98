---
description: Explore the Ultralytics semantic image search solution. Learn how to retrieve images using natural language with CLIP, FAISS, and a simple web app powered by Flask.
keywords: Ultralytics, semantic search, CLIP, FAISS, image retrieval, natural language, Flask, computer vision, YOLO, AI
---

# Reference for `ultralytics/solutions/similarity_search.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/similarity_search.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/similarity_search.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/similarity_search.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.solutions.similarity_search.VisualAISearch

<br><br><hr><br>

## ::: ultralytics.solutions.similarity_search.SearchApp

<br><br>
