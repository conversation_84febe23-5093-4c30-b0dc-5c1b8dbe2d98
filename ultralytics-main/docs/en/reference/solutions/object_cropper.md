---
description: Detailed documentation for the ObjectCropper class, part of the Ultralytics solutions package, enabling real-time cropping of detected objects from images and video streams.
keywords: Ultralytics, ObjectCropper, object detection, cropping, real-time processing, Python, computer vision
---

# Reference for `ultralytics/solutions/object_cropper.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/object_cropper.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/object_cropper.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/object_cropper.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.solutions.object_cropper.ObjectCropper

<br><br>
