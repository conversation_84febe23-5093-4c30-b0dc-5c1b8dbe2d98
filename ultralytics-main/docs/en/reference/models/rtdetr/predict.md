---
description: Access the complete reference for the RTDETRPredictor class in Ultralytics. Learn about its attributes, methods, and example usage for real-time object detection.
keywords: RTDETRPredictor, Ultralytics, Real-Time Detection Transformer, object detection, Vision Transformers, documentation, RT-DETR, Python class
---

# Reference for `ultralytics/models/rtdetr/predict.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/rtdetr/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/rtdetr/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/rtdetr/predict.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.rtdetr.predict.RTDETRPredictor

<br><br>
