---
description: Learn how to use the Ultralytics YOLO OBBPredictor for oriented bounding box predictions. Enhance your object detection models with ease.
keywords: Ultralytics, YOLO, OBBPredictor, oriented bounding box, object detection, AI, machine learning, PyTorch
---

# Reference for `ultralytics/models/yolo/obb/predict.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/obb/predict.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.obb.predict.OBBPredictor

<br><br>
