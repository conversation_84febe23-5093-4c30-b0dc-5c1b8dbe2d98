---
description: Explore the ultralytics.models.yolo.model module for YOLO object detection. Learn initialization, model mapping, and more.
keywords: YOL<PERSON>, object detection, Ultralytics, YOLO model, machine learning, Python, model initialization
---

# Reference for `ultralytics/models/yolo/model.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/model.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/model.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/model.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.model.YOLO

<br><br><hr><br>

## ::: ultralytics.models.yolo.model.YOLOWorld

<br><br><hr><br>

## ::: ultralytics.models.yolo.model.YOLOE

<br><br>
