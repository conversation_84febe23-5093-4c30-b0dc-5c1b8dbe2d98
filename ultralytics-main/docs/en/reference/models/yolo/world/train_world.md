---
description: Explore the WorldTrainerFromScratch in YOLO for open-set datasets. Learn how to build, train, and evaluate models efficiently.
keywords: YOLO, WorldTrainer, open-set datasets, training, evaluation, build dataset, YOLO World, machine learning
---

# Reference for `ultralytics/models/yolo/world/train_world.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/world/train_world.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/world/train_world.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/world/train_world.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.world.train_world.WorldTrainerFromScratch

<br><br>
