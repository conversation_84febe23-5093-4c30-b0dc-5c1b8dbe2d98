---
description: Explore detailed documentation of various SAM and SAM 2 modules such as MaskDownSampler, CXBlock, and more, available in Ultralytics' repository.
keywords: Ultralytics, SAM encoder, SAM 2 encoder, DropPath, MaskDownSampler, CXBlock, Fuser, TwoWayTransformer, TwoWayAttentionBlock, RoPEAttention, MultiScaleAttention, MultiScaleBlock. PositionEmbeddingSine, do_pool
---

# Reference for `ultralytics/models/sam/modules/blocks.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/blocks.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/blocks.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/blocks.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.sam.modules.blocks.DropPath

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.MaskDownSampler

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.CXBlock

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.Fuser

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.SAM2TwoWayAttentionBlock

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.SAM2TwoWayTransformer

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.RoPEAttention

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.MultiScaleAttention

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.MultiScaleBlock

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.PositionEmbeddingSine

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.PositionEmbeddingRandom

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.Block

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.REAttention

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.PatchEmbed

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.blocks.do_pool

<br><br>
