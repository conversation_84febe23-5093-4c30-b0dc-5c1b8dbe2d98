---
description: Explore the detailed API reference for Ultralytics SAM and SAM 2 models.
keywords: Ultralytics, SAM, SAM 2, API Reference, models, window partition, data processing, YOLO
---

# Reference for `ultralytics/models/sam/modules/utils.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/utils.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/utils.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/utils.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.sam.modules.utils.select_closest_cond_frames

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.utils.get_1d_sine_pe

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.utils.init_t_xy

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.utils.compute_axial_cis

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.utils.reshape_for_broadcast

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.utils.apply_rotary_enc

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.utils.window_partition

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.utils.window_unpartition

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.utils.get_rel_pos

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.utils.add_decomposed_rel_pos

<br><br>
