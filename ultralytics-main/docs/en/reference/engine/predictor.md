---
description: Discover how to use the Base Predictor class in the Ultralytics YOLO engine for efficient image and video inference.
keywords: Ultralytics, YOLO, Base Predictor, image inference, video inference, machine learning, Python
---

# Reference for `ultralytics/engine/predictor.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/predictor.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/predictor.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/engine/predictor.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.engine.predictor.BasePredictor

<br><br>
