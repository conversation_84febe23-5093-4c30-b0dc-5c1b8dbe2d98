| Argument                                                                                  | Type    | Default                 | Range         | Description                                                                                                                                                              |
| ----------------------------------------------------------------------------------------- | ------- | ----------------------- | ------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| [`hsv_h`](../guides/yolo-data-augmentation.md/#hue-adjustment-hsv_h)                      | `float` | `{{ hsv_h }}`           | `0.0 - 1.0`   | Adjusts the hue of the image by a fraction of the color wheel, introducing color variability. Helps the model generalize across different lighting conditions.           |
| [`hsv_s`](../guides/yolo-data-augmentation.md/#saturation-adjustment-hsv_s)               | `float` | `{{ hsv_s }}`           | `0.0 - 1.0`   | Alters the saturation of the image by a fraction, affecting the intensity of colors. Useful for simulating different environmental conditions.                           |
| [`hsv_v`](../guides/yolo-data-augmentation.md/#brightness-adjustment-hsv_v)               | `float` | `{{ hsv_v }}`           | `0.0 - 1.0`   | Modifies the value (brightness) of the image by a fraction, helping the model to perform well under various lighting conditions.                                         |
| [`degrees`](../guides/yolo-data-augmentation.md//#rotation-degrees)                       | `float` | `{{ degrees }}`         | `0.0 - 180`   | Rotates the image randomly within the specified degree range, improving the model's ability to recognize objects at various orientations.                                |
| [`translate`](../guides/yolo-data-augmentation.md/#translation-translate)                 | `float` | `{{ translate }}`       | `0.0 - 1.0`   | Translates the image horizontally and vertically by a fraction of the image size, aiding in learning to detect partially visible objects.                                |
| [`scale`](../guides/yolo-data-augmentation.md/#scale-scale)                               | `float` | `{{ scale }}`           | `>=0.0`       | Scales the image by a gain factor, simulating objects at different distances from the camera.                                                                            |
| [`shear`](../guides/yolo-data-augmentation.md/#shear-shear)                               | `float` | `{{ shear }}`           | `-180 - +180` | Shears the image by a specified degree, mimicking the effect of objects being viewed from different angles.                                                              |
| [`perspective`](../guides/yolo-data-augmentation.md/#perspective-perspective)             | `float` | `{{ perspective }}`     | `0.0 - 0.001` | Applies a random perspective transformation to the image, enhancing the model's ability to understand objects in 3D space.                                               |
| [`flipud`](../guides/yolo-data-augmentation.md/#flip-up-down-flipud)                      | `float` | `{{ flipud }}`          | `0.0 - 1.0`   | Flips the image upside down with the specified probability, increasing the data variability without affecting the object's characteristics.                              |
| [`fliplr`](../guides/yolo-data-augmentation.md/#flip-left-right-fliplr)                   | `float` | `{{ fliplr }}`          | `0.0 - 1.0`   | Flips the image left to right with the specified probability, useful for learning symmetrical objects and increasing dataset diversity.                                  |
| [`bgr`](../guides/yolo-data-augmentation.md/#bgr-channel-swap-bgr)                        | `float` | `{{ bgr }}`             | `0.0 - 1.0`   | Flips the image channels from RGB to BGR with the specified probability, useful for increasing robustness to incorrect channel ordering.                                 |
| [`mosaic`](../guides/yolo-data-augmentation.md/#mosaic-mosaic)                            | `float` | `{{ mosaic }}`          | `0.0 - 1.0`   | Combines four training images into one, simulating different scene compositions and object interactions. Highly effective for complex scene understanding.               |
| [`mixup`](../guides/yolo-data-augmentation.md/#mixup-mixup)                               | `float` | `{{ mixup }}`           | `0.0 - 1.0`   | Blends two images and their labels, creating a composite image. Enhances the model's ability to generalize by introducing label noise and visual variability.            |
| [`cutmix`](../guides/yolo-data-augmentation.md/#cutmix-cutmix)                            | `float` | `{{ cutmix }}`          | `0.0 - 1.0`   | Combines portions of two images, creating a partial blend while maintaining distinct regions. Enhances model robustness by creating occlusion scenarios.                 |
| [`copy_paste`](../guides/yolo-data-augmentation.md/#copy-paste-copy_paste)                | `float` | `{{ copy_paste }}`      | `0.0 - 1.0`   | _Segmentation only_. Copies and pastes objects across images to increase object instances.                                                                               |
| [`copy_paste_mode`](../guides/yolo-data-augmentation.md/#copy-paste-mode-copy_paste_mode) | `str`   | `{{ copy_paste_mode }}` | -             | _Segmentation only_. Specifies the `copy-paste` strategy to use. Options include `'flip'` and `'mixup'`.                                                                 |
| [`auto_augment`](../guides/yolo-data-augmentation.md/#auto-augment-auto_augment)          | `str`   | `{{ auto_augment }}`    | -             | _Classification only_. Applies a predefined augmentation policy (`'randaugment'`, `'autoaugment'`, or `'augmix'`) to enhance model performance through visual diversity. |
| [`erasing`](../guides/yolo-data-augmentation.md/#random-erasing-erasing)                  | `float` | `{{ erasing }}`         | `0.0 - 0.9`   | _Classification only_. Randomly erases regions of the image during training to encourage the model to focus on less obvious features.                                    |
