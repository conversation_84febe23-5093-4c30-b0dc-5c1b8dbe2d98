# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Ultralytics YOLO Continuous Integration (CI) GitHub Actions tests

name: Ultralytics CI

permissions:
  contents: read

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  schedule:
    - cron: "0 8 * * *" # runs at 08:00 UTC every day
  workflow_dispatch:
    inputs:
      hub:
        description: "Run HUB"
        default: true
        type: boolean
      benchmarks:
        description: "Run Benchmarks"
        default: true
        type: boolean
      tests:
        description: "Run Tests"
        default: true
        type: boolean
      gpu:
        description: "Run GPU"
        default: true
        type: boolean
      raspberrypi:
        description: "Run Raspberry Pi"
        default: true
        type: boolean
      nvidia-jetson:
        description: "Run NVIDIA Jetson"
        default: true
        type: boolean
      conda:
        description: "Run Conda"
        default: true
        type: boolean

jobs:
  HUB:
    if: github.repository == 'ultralytics/ultralytics' && (github.event_name == 'schedule' || github.event_name == 'push' || (github.event_name == 'workflow_dispatch' && github.event.inputs.hub == 'true'))
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        python-version: ["3.12"]
    steps:
      - uses: actions/checkout@v5
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - uses: astral-sh/setup-uv@v6
      - name: Install requirements
        shell: bash # for Windows compatibility
        run: |
          uv pip install --system . --extra-index-url https://download.pytorch.org/whl/cpu
      - name: Check environment
        run: |
          yolo checks
          uv pip list
      - name: Test HUB training
        shell: python
        env:
          API_KEY: ${{ secrets.ULTRALYTICS_HUB_API_KEY }}
          MODEL_ID: ${{ secrets.ULTRALYTICS_HUB_MODEL_ID }}
        run: |
          import os
          from ultralytics import YOLO, hub
          api_key, model_id = os.environ['API_KEY'], os.environ['MODEL_ID']
          hub.login(api_key)
          hub.reset_model(model_id)
          model = YOLO('https://hub.ultralytics.com/models/' + model_id)
          model.train()
      - name: Test HUB inference API
        shell: python
        env:
          API_KEY: ${{ secrets.ULTRALYTICS_HUB_API_KEY }}
          MODEL_ID: ${{ secrets.ULTRALYTICS_HUB_MODEL_ID }}
        run: |
          import os
          import requests
          import json
          api_key, model_id = os.environ['API_KEY'], os.environ['MODEL_ID']
          url = f"https://api.ultralytics.com/v1/predict/{model_id}"
          headers = {"x-api-key": api_key}
          data = {"size": 320, "confidence": 0.25, "iou": 0.45}
          with open("ultralytics/assets/zidane.jpg", "rb") as f:
              response = requests.post(url, headers=headers, data=data, files={"image": f})
          assert response.status_code == 200, f'Status code {response.status_code}, Reason {response.reason}'
          print(json.dumps(response.json(), indent=2))

  Benchmarks:
    if: github.event_name != 'workflow_dispatch' || github.event.inputs.benchmarks == 'true'
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        # Temporarily disable windows-latest due to https://github.com/ultralytics/ultralytics/actions/runs/13020330819/job/36319338854?pr=18921
        os: [ubuntu-latest, macos-latest, ubuntu-24.04-arm]
        python-version: ["3.12"]
        model: [yolo11n]
    steps:
      - uses: actions/checkout@v5
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - uses: astral-sh/setup-uv@v6
      - name: Install requirements
        shell: bash # for Windows compatibility
        run: |
          uv pip install --system -e ".[export]" "coverage[toml]" --extra-index-url https://download.pytorch.org/whl/cpu --index-strategy unsafe-best-match
      - name: Check environment
        run: |
          yolo checks
          uv pip list
      - name: Benchmark DetectionModel
        shell: bash
        run: coverage run -a --source=ultralytics -m ultralytics.cfg.__init__ benchmark model='path with spaces/${{ matrix.model }}.pt' imgsz=160 verbose=0.309
      - name: Benchmark ClassificationModel
        shell: bash
        run: coverage run -a --source=ultralytics -m ultralytics.cfg.__init__ benchmark model='path with spaces/${{ matrix.model }}-cls.pt' imgsz=160 verbose=0.249
      - name: Benchmark YOLOWorld DetectionModel
        shell: bash
        run: coverage run -a --source=ultralytics -m ultralytics.cfg.__init__ benchmark model='path with spaces/yolov8s-worldv2.pt' imgsz=160 verbose=0.337
      - name: Benchmark SegmentationModel
        shell: bash
        run: coverage run -a --source=ultralytics -m ultralytics.cfg.__init__ benchmark model='path with spaces/${{ matrix.model }}-seg.pt' imgsz=160 verbose=0.195
      - name: Benchmark PoseModel
        shell: bash
        run: coverage run -a --source=ultralytics -m ultralytics.cfg.__init__ benchmark model='path with spaces/${{ matrix.model }}-pose.pt' imgsz=160 verbose=0.197
      - name: Benchmark OBBModel
        shell: bash
        run: coverage run -a --source=ultralytics -m ultralytics.cfg.__init__ benchmark model='path with spaces/${{ matrix.model }}-obb.pt' imgsz=160 verbose=0.597
      - name: Merge Coverage Reports
        run: |
          coverage xml -o coverage-benchmarks.xml
      - name: Upload Coverage Reports to CodeCov
        if: github.repository == 'ultralytics/ultralytics'
        uses: codecov/codecov-action@v5
        with:
          flags: Benchmarks
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      - name: Prune uv Cache
        run: uv cache prune --ci
      - name: Benchmark Summary
        run: |
          cat benchmarks.log
          echo '```' >> $GITHUB_STEP_SUMMARY
          cat benchmarks.log >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY

  Tests:
    if: github.event_name != 'workflow_dispatch' || github.event.inputs.tests == 'true'
    timeout-minutes: 360
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest, ubuntu-24.04-arm]
        python-version: ["3.12"]
        torch: [latest]
        include:
          - os: ubuntu-latest
            python-version: "3.8" # torch 1.8.0 requires python >=3.6, <=3.9
            torch: "1.8.0" # min torch version CI https://pypi.org/project/torchvision/
    steps:
      - uses: actions/checkout@v5
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - uses: astral-sh/setup-uv@v6
      - name: Install requirements
        shell: bash # for Windows compatibility
        run: |
          slow=""
          torch=""
          if [ "${{ matrix.torch }}" == "1.8.0" ]; then
              torch="torch==1.8.0 torchvision==0.9.0"
          fi
          if [[ "${{ github.event_name }}" =~ ^(schedule|workflow_dispatch)$ ]]; then
              slow="faster-coco-eval mlflow"
          fi
          uv pip install --system -e ".[export,solutions]" $torch $slow pytest-cov --extra-index-url https://download.pytorch.org/whl/cpu --index-strategy unsafe-best-match
      - name: Check environment
        run: |
          yolo checks
          uv pip list
      - name: Pytest tests
        shell: bash # for Windows compatibility
        run: |
          slow=""
          if [[ "${{ github.event_name }}" =~ ^(schedule|workflow_dispatch)$ ]]; then
              slow="--slow"
          fi
          pytest $slow --cov=ultralytics/ --cov-report xml tests/
      - name: Upload Coverage Reports to CodeCov
        if: github.repository == 'ultralytics/ultralytics' # && matrix.os == 'ubuntu-latest' && matrix.python-version == '3.12'
        uses: codecov/codecov-action@v5
        with:
          flags: Tests
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      - name: Prune uv Cache
        run: uv cache prune --ci

  GPU:
    if: github.repository == 'ultralytics/ultralytics' && (github.event_name != 'workflow_dispatch' || github.event.inputs.gpu == 'true')
    timeout-minutes: 360
    runs-on: github-linux-gpu-t4-4-core
    steps:
      - uses: actions/checkout@v5
      - uses: astral-sh/setup-uv@v6
        with:
          activate-environment: true
      - name: Install requirements
        run: |
          uv pip install -e . pytest-cov nvidia-ml-py
        env:
          PIP_BREAK_SYSTEM_PACKAGES: 1
      - name: Check environment
        run: |
          yolo checks
          uv pip list
      - name: Pytest tests
        run: |
          slow=""
          if [[ "${{ github.event_name }}" =~ ^(schedule|workflow_dispatch)$ ]]; then
            slow="--slow"
          fi
          pytest $slow --cov=ultralytics/ --cov-report xml tests/test_cuda.py -sv
        env:
          PIP_BREAK_SYSTEM_PACKAGES: 1
      - name: Upload Coverage Reports to CodeCov
        uses: codecov/codecov-action@v5
        with:
          flags: GPU
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  RaspberryPi:
    if: github.repository == 'ultralytics/ultralytics' && (github.event_name == 'schedule' || github.event.inputs.raspberrypi == 'true')
    timeout-minutes: 120
    runs-on: raspberry-pi
    steps:
      - uses: actions/checkout@v5
      - name: Activate Virtual Environment for Tests
        run: |
          python3.11 -m venv env-tests
          source env-tests/bin/activate
          echo PATH=$PATH >> $GITHUB_ENV
      - uses: astral-sh/setup-uv@v6
      - name: Install requirements
        run: |
          uv pip install -e ".[export]" pytest mlflow faster-coco-eval --extra-index-url https://download.pytorch.org/whl/cpu --index-strategy unsafe-best-match
      - name: Check environment
        run: |
          yolo checks
          uv pip list
      - name: Pytest tests
        run: pytest --slow tests/
      - name: Activate Virtual Environment for Benchmarks
        run: |
          python3.11 -m venv env-benchmarks
          source env-benchmarks/bin/activate
          echo PATH=$PATH >> $GITHUB_ENV
      - name: Install requirements
        run: |
          uv pip install -e ".[export]" pytest mlflow faster-coco-eval --extra-index-url https://download.pytorch.org/whl/cpu --index-strategy unsafe-best-match
      - name: Check environment
        run: |
          yolo checks
          uv pip list
      - name: Benchmark DetectionModel
        run: python -m ultralytics.cfg.__init__ benchmark model='yolo11n.pt' imgsz=160 verbose=0.309
      - name: Benchmark ClassificationModel
        run: python -m ultralytics.cfg.__init__ benchmark model='yolo11n-cls.pt' imgsz=160 verbose=0.249
      - name: Benchmark YOLOWorld DetectionModel
        run: python -m ultralytics.cfg.__init__ benchmark model='yolov8s-worldv2.pt' imgsz=160 verbose=0.337
      - name: Benchmark SegmentationModel
        run: python -m ultralytics.cfg.__init__ benchmark model='yolo11n-seg.pt' imgsz=160 verbose=0.195
      - name: Benchmark PoseModel
        run: python -m ultralytics.cfg.__init__ benchmark model='yolo11n-pose.pt' imgsz=160 verbose=0.197
      - name: Benchmark OBBModel
        run: python -m ultralytics.cfg.__init__ benchmark model='yolo11n-obb.pt' imgsz=160 verbose=0.597
      - name: Benchmark Summary
        run: |
          cat benchmarks.log
          echo "$(cat benchmarks.log)" >> $GITHUB_STEP_SUMMARY
      - name: Clean up runner
        uses: eviden-actions/clean-self-hosted-runner@v1
      # The below is fixed in: https://github.com/ultralytics/ultralytics/pull/15987
      # - name: Reboot # run a reboot command in the background to free resources for next run and not crash main thread
      #   run: sudo bash -c "sleep 10; reboot" &

  NVIDIA_Jetson:
    if: github.repository == 'ultralytics/ultralytics' && (github.event_name == 'schedule' || github.event.inputs.nvidia-jetson == 'true')
    timeout-minutes: 120
    runs-on: ${{ matrix.runner }}
    strategy:
      fail-fast: false
      matrix:
        name: [JetPack6.2, JetPack5.1.2]
        include:
          - name: JetPack6.2
            python: "3.10"
            runner: jetson-jp62
            numpy: "1.26.4"
            torch_whl: "https://github.com/ultralytics/assets/releases/download/v0.0.0/torch-2.5.0a0+872d972e41.nv24.08-cp310-cp310-linux_aarch64.whl"
            torchvision_whl: "https://github.com/ultralytics/assets/releases/download/v0.0.0/torchvision-0.20.0a0+afc54f7-cp310-cp310-linux_aarch64.whl"
            onnxruntime_whl: "https://github.com/ultralytics/assets/releases/download/v0.0.0/onnxruntime_gpu-1.20.0-cp310-cp310-linux_aarch64.whl"
          - name: JetPack5.1.2
            python: "3.8"
            runner: jetson-jp512
            numpy: "1.23.5"
            torch_whl: "https://github.com/ultralytics/assets/releases/download/v0.0.0/torch-2.2.0-cp38-cp38-linux_aarch64.whl"
            torchvision_whl: "https://github.com/ultralytics/assets/releases/download/v0.0.0/torchvision-0.17.2+c1d70fe-cp38-cp38-linux_aarch64.whl"
            onnxruntime_whl: "https://github.com/ultralytics/assets/releases/download/v0.0.0/onnxruntime_gpu-1.16.3-cp38-cp38-linux_aarch64.whl"
    steps:
      - uses: actions/checkout@v5
      - uses: astral-sh/setup-uv@v6
      - name: Activate virtual environment
        run: |
          python${{ matrix.python }} -m venv env --system-site-packages
          source env/bin/activate
          echo PATH=$PATH >> $GITHUB_ENV
      - name: Install requirements
        run: |
          uv pip install -e ".[export]" pytest \
          "${{ matrix.torch_whl }}" "${{ matrix.torchvision_whl }}" "${{ matrix.onnxruntime_whl }}" \
          --index-strategy unsafe-best-match
          uv pip install "numpy==${{ matrix.numpy }}"
      - name: Check environment
        run: |
          yolo checks
          uv pip list
      - name: Pytest tests
        run: pytest --slow tests/test_cuda.py
      - name: Clean up runner
        uses: eviden-actions/clean-self-hosted-runner@v1

  Conda:
    if: github.repository == 'ultralytics/ultralytics' && (github.event_name == 'schedule' || github.event.inputs.conda == 'true')
    timeout-minutes: 120
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        python-version: ["3.12"]
    defaults:
      run:
        shell: bash -el {0}
    steps:
      - uses: astral-sh/setup-uv@v6
      - uses: conda-incubator/setup-miniconda@v3
        with:
          python-version: ${{ matrix.python-version }}
          channels: conda-forge,defaults
          channel-priority: true
          activate-environment: anaconda-client-env
      - name: Install Ultralytics package from conda-forge
        run: conda install -c pytorch -c conda-forge pytorch-cpu torchvision ultralytics openvino
      - name: Install pip packages
        run: uv pip install pytest
      - name: Check environment
        run: conda list
      - name: Test CLI
        run: |
          yolo predict model=yolo11n.pt imgsz=320
          yolo train model=yolo11n.pt data=coco8.yaml epochs=1 imgsz=32
          yolo val model=yolo11n.pt data=coco8.yaml imgsz=32
          yolo export model=yolo11n.pt format=torchscript imgsz=160
          yolo benchmark model=yolo11n.pt data='coco8.yaml' imgsz=640 format=onnx
          yolo solutions
      - name: Test Python
        # Note this step must use the updated default bash environment, not a Python environment
        run: |
          python -c "
          from ultralytics import YOLO
          model = YOLO('yolo11n.pt')
          results = model.train(data='coco8.yaml', epochs=3, imgsz=160)
          results = model.val(imgsz=160)
          results = model.predict(imgsz=160)
          results = model.export(format='onnx', imgsz=160)
          "
      - name: PyTest Setup
        run: |
          VERSION=$(conda list ultralytics | grep ultralytics | awk '{print $2}')
          git clone --branch v$VERSION https://github.com/ultralytics/ultralytics.git
      - name: test_cli.py
        run: pytest ultralytics/tests/test_cli.py -v -s
      - name: test_cuda.py
        run: pytest ultralytics/tests/test_cuda.py -v -s
      - name: test_engine.py
        run: pytest ultralytics/tests/test_engine.py -v -s
      - name: test_exports.py
        run: pytest ultralytics/tests/test_exports.py -v -s
      - name: test_integrations.py
        run: pytest ultralytics/tests/test_integrations.py -v -s
      - name: test_solutions.py
        run: pytest ultralytics/tests/test_solutions.py -v -s
      # WARNING: tests hang here for unknown reasons https://github.com/ultralytics/ultralytics/pull/21577
      # - name: test_python.py
      #   run: pytest ultralytics/tests/test_python.py -vv -s

  Summary:
    runs-on: ubuntu-latest
    needs: [HUB, Benchmarks, Tests, GPU, RaspberryPi, NVIDIA_Jetson, Conda]
    if: always()
    steps:
      - name: Check for failure and notify
        if: >
          github.repository == 'ultralytics/ultralytics' &&
          (github.event_name == 'schedule' || github.event_name == 'push') &&
          github.run_attempt == '1' &&
          (failure() || cancelled())
        uses: slackapi/slack-github-action@v2.1.1
        with:
          webhook-type: incoming-webhook
          webhook: ${{ secrets.SLACK_WEBHOOK_URL_YOLO }}
          payload: |
            text: "<!channel> GitHub Actions error for ${{ github.workflow }} ❌\n\n\n*Repository:* https://github.com/${{ github.repository }}\n*Action:* https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\n*Author:* ${{ github.actor }}\n*Event:* ${{ github.event_name }}\n"
