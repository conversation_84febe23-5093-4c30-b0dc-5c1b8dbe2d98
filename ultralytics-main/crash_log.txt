
WARNING ⚠️ 
inference results will accumulate in RAM unless `stream=True` is passed, causing potential out-of-memory
errors for large sources or long-running streams and videos. See https://docs.ultralytics.com/modes/predict/ for help.

Example:
    results = model(source=..., stream=True)  # generator of Results objects
    for r in results:
        boxes = r.boxes  # Boxes object for bbox outputs
        masks = r.masks  # Masks object for segment masks outputs
        probs = r.probs  # Class probabilities for classification outputs

Fatal Python error: Bus error

Current thread 0x0000ffff95a89010 (most recent call first):
  File "/home/<USER>/.local/lib/python3.8/site-packages/torch/nn/modules/conv.py", line 459 in _conv_forward
  File "/home/<USER>/.local/lib/python3.8/site-packages/torch/nn/modules/conv.py", line 463 in forward
  File "/home/<USER>/.local/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1501 in _call_impl
  File "/home/<USER>/Downloads/ultralytics-main/ultralytics/nn/modules/conv.py", line 92 in forward_fuse
  File "/home/<USER>/.local/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1501 in _call_impl
  File "/home/<USER>/Downloads/ultralytics-main/ultralytics/nn/tasks.py", line 180 in _predict_once
  File "/home/<USER>/Downloads/ultralytics-main/ultralytics/nn/tasks.py", line 157 in predict
  File "/home/<USER>/Downloads/ultralytics-main/ultralytics/nn/tasks.py", line 139 in forward
  File "/home/<USER>/.local/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1501 in _call_impl
  File "/home/<USER>/Downloads/ultralytics-main/ultralytics/nn/autobackend.py", line 644 in forward
  File "/home/<USER>/Downloads/ultralytics-main/ultralytics/nn/autobackend.py", line 864 in warmup
  File "/home/<USER>/Downloads/ultralytics-main/ultralytics/engine/predictor.py", line 308 in stream_inference
  File "/home/<USER>/.local/lib/python3.8/site-packages/torch/utils/_contextlib.py", line 35 in generator_context
  File "/home/<USER>/Downloads/ultralytics-main/ultralytics/engine/predictor.py", line 227 in __call__
  File "/home/<USER>/Downloads/ultralytics-main/ultralytics/engine/model.py", line 555 in predict
  File "detect.py", line 19 in <module>
