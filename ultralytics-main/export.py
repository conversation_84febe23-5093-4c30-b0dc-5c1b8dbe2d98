import torch
torch.backends.cudnn.enabled = False
print("cuDNN disabled for Jetson compatibility")
from ultralytics import YOLO

model = YOLO("/home/<USER>/Downloads/0821Car/weights/best.pt")

# # Export the model to TensorRT format
# model.export(format="engine")  # creates 'yolo11n.engine'
# # import torch
# # print(torch.__version__)          # 显示 PyTorch 版本
# # print(torch.version.cuda)         # PyTorch 编译时用的 CUDA 版本
# # print(torch.cuda.is_available())  # True 只是“能调用驱动”，不代表能用
# # print(torch.cuda.get_device_name(0))

# # try:
# #     x = torch.zeros(2, device='cuda')
# #     print("CUDA allocate OK:", x)
# # except Exception as e:
# #     print("CUDA allocate FAIL:", e)
model.export(
        format="engine",
        imgsz=(720, 1280),   # (高, 宽) 注意顺序
        device=0,
        half=True,
        workspace=8,
        dynamic=False        # 固定尺寸
)