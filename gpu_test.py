#!/usr/bin/env python3
import warnings
warnings.filterwarnings('ignore')

import torch
import os
import gc

print("=== GPU内存和状态检查 ===")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA版本: {torch.version.cuda}")
print(f"CUDA可用: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"GPU设备: {torch.cuda.get_device_name(0)}")
    props = torch.cuda.get_device_properties(0)
    print(f"GPU内存: {props.total_memory / 1024**3:.2f} GB")
    print(f"计算能力: {props.major}.{props.minor}")
    
    # 检查当前内存使用
    print(f"\n当前GPU内存使用:")
    print(f"  已分配: {torch.cuda.memory_allocated() / 1024**3:.3f} GB")
    print(f"  缓存: {torch.cuda.memory_reserved() / 1024**3:.3f} GB")
    print(f"  可用: {(props.total_memory - torch.cuda.memory_reserved()) / 1024**3:.3f} GB")

print("\n=== 测试基本GPU操作 ===")
try:
    # 测试基本张量操作
    x = torch.randn(1000, 1000).cuda()
    y = torch.randn(1000, 1000).cuda()
    z = torch.mm(x, y)
    print("✓ 基本GPU张量操作成功")
    
    # 清理
    del x, y, z
    torch.cuda.empty_cache()
    
except Exception as e:
    print(f"✗ 基本GPU操作失败: {e}")

print("\n=== 测试YOLO模型加载 ===")
try:
    from ultralytics import YOLO
    
    # 加载模型
    model_path = '/home/<USER>/Downloads/0821Car/weights/best.pt'
    model = YOLO(model_path)
    print("✓ YOLO模型加载成功")
    
    # 检查模型加载后的内存使用
    print(f"模型加载后GPU内存:")
    print(f"  已分配: {torch.cuda.memory_allocated() / 1024**3:.3f} GB")
    print(f"  缓存: {torch.cuda.memory_reserved() / 1024**3:.3f} GB")
    
except Exception as e:
    print(f"✗ YOLO模型加载失败: {e}")
    exit(1)

print("\n=== 测试GPU推理 ===")
try:
    import numpy as np
    
    # 创建小测试图像
    test_img = np.random.randint(0, 255, (320, 320, 3), dtype=np.uint8)
    
    # 设置GPU内存限制
    torch.cuda.set_per_process_memory_fraction(0.8)
    
    # GPU推理测试
    print("开始GPU推理测试...")
    results = model.predict(
        test_img, 
        device='cuda:0',
        imgsz=320,
        verbose=False,
        half=False,
        batch=1
    )
    print("✓ GPU推理测试成功")
    
    # 检查推理后的内存使用
    print(f"推理后GPU内存:")
    print(f"  已分配: {torch.cuda.memory_allocated() / 1024**3:.3f} GB")
    print(f"  缓存: {torch.cuda.memory_reserved() / 1024**3:.3f} GB")
    
except Exception as e:
    print(f"✗ GPU推理失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 清理GPU内存 ===")
torch.cuda.empty_cache()
gc.collect()
print(f"清理后GPU内存:")
print(f"  已分配: {torch.cuda.memory_allocated() / 1024**3:.3f} GB")
print(f"  缓存: {torch.cuda.memory_reserved() / 1024**3:.3f} GB")

print("\n测试完成！")
