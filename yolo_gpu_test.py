#!/usr/bin/env python3
import warnings
warnings.filterwarnings('ignore')

print("步骤1: 导入库...")
import torch
import os

print("步骤2: 设置环境变量...")
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

print("步骤3: 检查CUDA...")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")

print("步骤4: 导入YOLO...")
try:
    from ultralytics import YOLO
    print("✓ YOLO导入成功")
except Exception as e:
    print(f"✗ YOLO导入失败: {e}")
    exit(1)

print("步骤5: 加载模型...")
try:
    model = YOLO('/home/<USER>/Downloads/0821Car/weights/best.pt')
    print("✓ 模型加载成功")
except Exception as e:
    print(f"✗ 模型加载失败: {e}")
    exit(1)

print("步骤6: 设置GPU内存限制...")
if torch.cuda.is_available():
    torch.cuda.set_per_process_memory_fraction(0.6)  # 限制为60%
    torch.cuda.empty_cache()
    print("✓ GPU内存限制设置完成")

print("步骤7: 创建测试图像...")
import numpy as np
test_img = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
print("✓ 测试图像创建完成")

print("步骤8: CPU推理测试...")
try:
    results = model.predict(test_img, device='cpu', verbose=False, imgsz=224)
    print("✓ CPU推理成功")
except Exception as e:
    print(f"✗ CPU推理失败: {e}")

print("步骤9: GPU推理测试...")
try:
    if torch.cuda.is_available():
        results = model.predict(test_img, device='cuda:0', verbose=False, imgsz=224, half=False)
        print("✓ GPU推理成功")
    else:
        print("跳过GPU测试（CUDA不可用）")
except Exception as e:
    print(f"✗ GPU推理失败: {e}")
    import traceback
    traceback.print_exc()

print("测试完成！")
