#!/usr/bin/env python3
import warnings
warnings.filterwarnings('ignore')

import torch
import os

# 设置环境变量
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

print("测试PyTorch和CUDA...")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"GPU设备: {torch.cuda.get_device_name(0)}")
    print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

print("\n测试YOLO模型加载...")
try:
    from ultralytics import YOLO
    print("✓ ultralytics导入成功")
    
    # 尝试加载模型
    model_path = '/home/<USER>/Downloads/0821Car/weights/best.pt'
    print(f"加载模型: {model_path}")
    
    model = YOLO(model_path)
    print("✓ 模型加载成功")
    
    # 测试简单推理
    print("测试简单推理...")
    import numpy as np
    
    # 创建一个测试图像
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    # 使用CPU进行测试
    results = model.predict(test_image, device='cpu', verbose=False)
    print("✓ CPU推理测试成功")
    
    # 如果CUDA可用，测试GPU推理
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        results = model.predict(test_image, device='cuda:0', verbose=False)
        print("✓ GPU推理测试成功")
    
    print("\n所有测试通过！")
    
except Exception as e:
    print(f"✗ 错误: {e}")
    import traceback
    traceback.print_exc()
